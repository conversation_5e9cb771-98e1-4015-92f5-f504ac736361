import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true); // <-- start true
  const [authKey, setAuthKey] = useState(0); // Force re-render key

  // Mock user database
  const mockUsers = [
    { id: 1, username: 'admin', password: 'admin123', role: 'admin', name: 'Administrator' },
    { id: 2, username: 'user1', password: 'user123', role: 'user', name: 'Regular User' },
    { id: 3, username: 'donor', password: 'donor123', role: 'donor', name: 'Donor User' }
  ];

  // Restore user on app start
  useEffect(() => {
    const restoreUser = async () => {
      try {
        const storedUser = await AsyncStorage.getItem('user');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Error restoring user:', error);
      } finally {
        setIsLoading(false);
      }
    };
    restoreUser();
  }, []);

  // Login
  const login = async (username, password) => {
    try {
      const foundUser = mockUsers.find(
        u => u.username === username && u.password === password
      );

      if (foundUser) {
        const { password: _, ...userWithoutPassword } = foundUser;

        await AsyncStorage.setItem('user', JSON.stringify(userWithoutPassword));

        setUser(userWithoutPassword);
        setIsAuthenticated(true);
        setAuthKey(prev => prev + 1); // re-render navigation

        return { success: true, user: userWithoutPassword };
      } else {
        return { success: false, error: 'Invalid username or password' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Login failed. Please try again.' };
    }
  };

  // Logout
  const logout = async () => {
    console.log('🔓 Logout function called');
    try {
      await AsyncStorage.removeItem('user');
      console.log('🗑️ User removed from storage');

      setUser(null);
      setIsAuthenticated(false);
      setAuthKey(prev => prev + 1); // trigger re-render
      console.log('✅ State cleared - logged out');
    } catch (error) {
      console.error('❌ Logout error:', error);
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  // Clear storage
  const clearStorage = async () => {
    try {
      await AsyncStorage.clear();
      console.log('All AsyncStorage cleared');
      setUser(null);
      setIsAuthenticated(false);
      setAuthKey(prev => prev + 1);
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  };

  const isAdmin = () => user?.role === 'admin';
  const hasRole = role => user?.role === role;

  const value = {
    isAuthenticated,
    user,
    isLoading,
    authKey,
    login,
    logout,
    clearStorage,
    isAdmin,
    hasRole,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within an AuthProvider');
  return context;
};

export default AuthContext;